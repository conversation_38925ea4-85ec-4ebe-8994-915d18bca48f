import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import "./button.css";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  asChild?: boolean;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className = "",
      variant = "default",
      size = "default",
      asChild = false,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";

    const getButtonClasses = () => {
      let classes = "btn";

      // 添加变体类
      switch (variant) {
        case "default":
          classes += " btn-default";
          break;
        case "destructive":
          classes += " btn-destructive";
          break;
        case "outline":
          classes += " btn-outline";
          break;
        case "secondary":
          classes += " btn-secondary";
          break;
        case "ghost":
          classes += " btn-ghost";
          break;
        case "link":
          classes += " btn-link";
          break;
      }

      // 添加尺寸类
      switch (size) {
        case "default":
          classes += " btn-default-size";
          break;
        case "sm":
          classes += " btn-sm";
          break;
        case "lg":
          classes += " btn-lg";
          break;
        case "icon":
          classes += " btn-icon";
          break;
      }

      // 添加自定义类名
      if (className) {
        classes += ` ${className}`;
      }

      return classes;
    };

    return <Comp className={getButtonClasses()} ref={ref} {...props} />;
  }
);
Button.displayName = "Button";

export { Button };
