/* SecondaryMenu 组件样式 */

/* 主容器样式 */
.secondary-menu {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

/* 菜单容器样式 */
.menu-container {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 8px 16px;
  background-color: #FFFFFF;
  max-height: 48px;
  white-space: nowrap;
}

.menu-container-wide {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

/* 图标样式 */
.icon-small {
  height: 16px;
  width: 16px;
}

.icon-color{
  height: 8px;
  width: 16px;
}

.icon-medium {
  height: 24px;
  width: 24px;
}

/* 按钮样式 */
.button-small {
  height: 32px;
  padding: 0 8px;
}

.button-square {
  height: 32px;
  width: 32px;
  padding: 0;
}

.button-active {
  background-color: #E6E6E6!important;
  border-color: #E6E6E6 !important;
  /* box-shadow: 0 0 0 1px #E6E6E6 !important; */
}

/* 文本样式 */
.text-small {
  font-size: 14px;
}

.text-extra-small {
  font-size: 12px;
}

.text-gray {
  color: #6b7280;
}

/* 间距样式 */
.margin-left-small {
  margin-left: 4px;
}

.margin-left-medium {
  margin-left: 2px;
}

/* 分隔符样式 */
.separator {
  height: 24px;
}

/* Flex 布局样式 */
.flex-center {
  display: flex;
  align-items: center;
}

.flex-center-gap {
  display: flex;
  align-items: center;
  gap: 8px;
}

.flex-center-gap-small {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Popover 内容样式 */
.popover-content-narrow {
  width: 128px;
  padding: 8px;
}

.popover-content-border {
  width: 120px;
  padding: 4px;
}

/* 边框宽度列表 */
.border-width-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 边框宽度选项 */
.border-width-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 12px;
  width: 100%;
  height: auto;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.border-width-item:hover {
  background-color: #f3f4f6;
}

/* 选中状态 */
.border-width-selected {
  background-color: #e5e7eb !important;
  color: #3b82f6;
}

.border-width-selected:hover {
  background-color: #e5e7eb !important;
}

/* 勾选图标 */
.check-icon {
  margin-right: 8px;
  color: #3b82f6;
  flex-shrink: 0;
  transition: opacity 0.2s ease;
}

.check-icon-visible {
  opacity: 1;
}

.check-icon-hidden {
  opacity: 0;
}

/* 边框宽度文本 */
.border-width-text {
  font-size: 14px;
  line-height: 1;
  color: #374151;
}

.popover-content-color {
  width: auto;
  padding: 0;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.popover-content-wide {
  width: 192px;
  padding: 8px;
}

/* 网格布局样式 */
.grid-single-column {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4px;
}

.grid-five-columns {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
}

/* 按钮内容对齐 */
.justify-start {
  justify-content: flex-start;
}


/* 颜色按钮样式 */
.color-button {
  height: 32px;
  width: 32px;
  padding: 0;
  border: 1px solid #e5e7eb;
  transition: transform 0.2s;
}

.color-button:hover {
  transform: scale(1.1);
}

/* 相对定位 */
.relative {
  position: relative;
}

/* 绝对定位的颜色指示器 */
.color-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 8px;
  height: 4px;
  border-radius: 2px;
}


