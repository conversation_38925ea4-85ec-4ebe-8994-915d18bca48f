/* 主菜单容器 */
.context-menu {
  position: fixed;
  z-index: 50;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 4px 0;
  min-height: 130px;
  min-width: 250px;
}

/* 菜单项按钮 */
.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  font-size: 14px;
  text-align: left;
  border: none;
  background: none;
  transition: background-color 0.15s ease-in-out;
}

.menu-item:not(:disabled) {
  /* color: #374151; */
  cursor: pointer;
}

.menu-item:not(:disabled):hover {
  background-color: transparent; /* 将背景色设置为透明 */
}

.menu-item:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* 菜单项图标 */
.menu-item-icon {
  margin-right: 12px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 菜单项标签 */
.menu-item-label {
  flex: 1;
}

/* 菜单项描述 */
.menu-item-describe {
  color: #9ca3af;
  font-size: 14px;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 菜单分隔符 */
.menu-separator {
  border-top: 1px solid #e5e7eb;
  margin: 4px 0;
}

/* 图标尺寸 */
.icon-small {
  width: 16px;
  height: 16px;
}
