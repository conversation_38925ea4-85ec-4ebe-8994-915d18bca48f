/* TopMenu 组件样式 */

/* 主容器 */
.top-menu {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-menu .menu-container{
  justify-content: center;
}
/* 菜单项容器 */
.menu-items {
  display: flex;
  gap: 4px;
}

/* 菜单项按钮 */
.menu-item {
  padding: 8px 16px;
  border-radius: 6px 6px 0 0;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  position: relative;
  border: none;
  background: none;
  cursor: pointer;
}

/* 默认状态 */
.menu-item:not(.active) {
  color: #374151;
}

.menu-item:not(.active):hover {
  background-color: #f3f4f6;
}

/* 激活状态 */
.menu-item.active {
  background-color: white;
  color: #0d9488;
  border-bottom: 2px solid #0d9488;
}
