import type { MenuType } from "../types/mindmap";
import "./TopMenu.css";

interface TopMenuProps {
  activeMenu: MenuType | null;
  onMenuChange: (menu: MenuType | null) => void;
}

const menuItems: { key: MenuType; label: string }[] = [
  { key: "start", label: "开始" },
  { key: "style", label: "样式" },
  { key: "insert", label: "插入" },
  { key: "view", label: "视图" },
  { key: "export", label: "导出" },
];

export default function TopMenu({ activeMenu, onMenuChange }: TopMenuProps) {
  const handleMenuClick = (menuKey: MenuType) => {
    if (activeMenu === menuKey) {
      // 点击已激活的菜单项时不收起，保持显示
      return;
    } else {
      onMenuChange(menuKey);
    }
  };

  return (
    <div className="top-menu">
      <div className="menu-container">
        <div className="menu-items">
          {menuItems.map((item) => (
            <button
              key={item.key}
              type="button"
              onClick={() => handleMenuClick(item.key)}
              className={`menu-item ${activeMenu === item.key ? "active" : ""}`}
            >
              {item.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
