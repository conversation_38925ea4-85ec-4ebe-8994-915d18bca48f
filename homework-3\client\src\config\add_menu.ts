// import addSon from "../icon/add/addSon.png";
// import addBrother_icon from "../icon/add/addBrother.png";
// import addParents_icon from "../icon/add/addParents.png";

import crsuyuanIcon from "../icon/add/crsuyuan.png";

// 定义图标按钮配置的类型
export interface add_IconConfig {
  icon: string;   // 图标的路径
  label: string;  // 按钮的标签
  describe:string
  onClick: () => void;  // 按钮点击事件
  addMenuSeparator?: boolean; // 是否在按钮后添加 Separator

}

// 按钮配置数组，包含图标、标签和点击事件
export const add_icons: add_IconConfig[] = [
  // { icon: addSon, label: '新增子主题', describe: 'Tab', onClick: () => {} },
  // { icon: addBrother_icon, label: '新增同级主题', describe: 'Enter', onClick: () => {} },
  // { icon: addParents_icon, label: '新增父主题', describe: 'Shift + Tab', onClick: () => {} },
  { icon: '', label: '插入', describe: '', onClick: () => {} },
  { icon: '', label: '编号', describe: '', onClick: () => {},addMenuSeparator: true },
  { icon: '', label: '收起主题', describe: '', onClick: () => {} },
  { icon: '', label: '选择主题', describe: '', onClick: () => {} ,addMenuSeparator: true},
  { icon: '', label: '复制', describe: 'Ctrl + C', onClick: () => {} },
  { icon: '', label: '剪切', describe: 'Ctrl + X', onClick: () => {} },
  { icon: '', label: '粘贴主题', describe: 'Ctrl + V', onClick: () => {} },
  { icon: '', label: '删除', describe: 'Del', onClick: () => {} },
  { icon: '', label: '删除当前主题', describe: 'Ctrl + Del', onClick: () => {} },
  { icon: '', label: '批量删除', describe: '', onClick: () => {},addMenuSeparator: true },
  { icon: '', label: '聚焦模式', describe: 'Ctrl + `', onClick: () => {} },
  { icon: crsuyuanIcon, label: '导出/复制当前主题', describe: ' ', onClick: () => {} },
];
